import { useCallback, useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { usePrivyLogin } from "./usePrivyLogin";
import { toastError, toastSuccess } from "@/libs/toast";
import Storage from "@/libs/storage";
import { LOGIN_METHODS } from "@/utils/contants";

export const usePrivyTradingEnablement = () => {
  const { user: privyUser, authenticated } = usePrivy();
  const { handlerAddSignerSession } = usePrivyLogin();
  const [isEnabling, setIsEnabling] = useState(false);
  console.log(privyUser, "privyUser");

  const isPrivyUser = Storage.getLoginMethod() === LOGIN_METHODS.PRIVY;
  const isTradingEnabled = privyUser?.wallet?.delegated || false;
  const hasPrivyWallet = !!privyUser?.wallet?.address;

  const enableTrading = useCallback(async () => {
    if (!isPrivyUser) {
      toastError(
        "Error",
        "Trading enablement is only available for Privy users"
      );
      return false;
    }

    if (!hasPrivyWallet) {
      toastError(
        "Error",
        "No Privy wallet found. Please create a wallet first."
      );
      return false;
    }

    if (isTradingEnabled) {
      toastSuccess("Info", "Trading is already enabled for this wallet");
      return true;
    }

    setIsEnabling(true);
    try {
      await handlerAddSignerSession(privyUser.wallet!.address);
      toastSuccess("Success", "Trading has been enabled successfully!");
      return true;
    } catch (error) {
      console.error("Failed to enable trading:", error);
      toastError("Error", "Failed to enable trading. Please try again.");
      return false;
    } finally {
      setIsEnabling(false);
    }
  }, [
    isPrivyUser,
    hasPrivyWallet,
    isTradingEnabled,
    privyUser?.wallet?.address,
    handlerAddSignerSession,
  ]);

  return {
    isPrivyUser,
    isTradingEnabled,
    hasPrivyWallet,
    isEnabling,
    enableTrading,
  };
};
