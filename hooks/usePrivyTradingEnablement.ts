import { useCallback, useState } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { usePrivyLogin } from "./usePrivyLogin";
import { toastError, toastSuccess } from "@/libs/toast";
import Storage from "@/libs/storage";
import { LOGIN_METHODS } from "@/utils/contants";

export const usePrivyTradingEnablement = () => {
  const { user: privyUser, authenticated } = usePrivy();
  const { handlerAddSignerSession } = usePrivyLogin();
  const [isEnabling, setIsEnabling] = useState(false);
  console.log(privyUser, "privyUser");

  const isPrivyUser = Storage.getLoginMethod() === LOGIN_METHODS.PRIVY;

  // Find the Sui wallet in linkedAccounts
  const suiWallet = privyUser?.linkedAccounts?.find(
    (account: any) => account.type === "wallet" && account.chainType === "sui"
  ) as any;

  // Check if trading is already enabled (session signers are delegated)
  const isTradingEnabled = suiWallet?.delegated || false;

  // Check if user has a Privy wallet
  const hasPrivyWallet = !!suiWallet?.address;

  const enableTrading = useCallback(async () => {
    if (!isPrivyUser) {
      toastError(
        "Error",
        "Trading enablement is only available for Privy users"
      );
      return false;
    }

    if (!hasPrivyWallet) {
      toastError(
        "Error",
        "No Privy wallet found. Please create a wallet first."
      );
      return false;
    }

    if (isTradingEnabled) {
      toastSuccess("Info", "Trading is already enabled for this wallet");
      return true;
    }

    setIsEnabling(true);
    try {
      await handlerAddSignerSession(suiWallet.address);
      toastSuccess("Success", "Trading has been enabled successfully!");
      return true;
    } catch (error) {
      console.error("Failed to enable trading:", error);
      toastError("Error", "Failed to enable trading. Please try again.");
      return false;
    } finally {
      setIsEnabling(false);
    }
  }, [
    isPrivyUser,
    hasPrivyWallet,
    isTradingEnabled,
    suiWallet?.address,
    handlerAddSignerSession,
  ]);

  return {
    isPrivyUser,
    isTradingEnabled,
    hasPrivyWallet,
    isEnabling,
    enableTrading,
  };
};
