import { useIdentityToken, usePrivy, useWallets } from "@privy-io/react-auth";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { AppDispatch, RootState } from "@/store";
import { setUserAuth } from "@/store/user.store";
import Storage from "@/libs/storage";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import rf from "@/services/RequestFactory";
import { useSessionSigners } from "@privy-io/react-auth";
import config from "@/config";
import { useCreateWallet } from "@privy-io/react-auth/extended-chains";

export const usePrivyLogin = () => {
  const { login, authenticated, user: privyUser } = usePrivy();
  const { identityToken } = useIdentityToken();
  const { ready: walletsReady } = useWallets();
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const userId = useSelector((state: RootState) => state.user.userId);
  const { addSessionSigners, removeSessionSigners } = useSessionSigners();

  const exchangePrivyForJWT = useCallback(async (idToken: string) => {
    try {
      const response = await rf.getRequest("PrivyRequest").login(idToken);
      return response.jwtToken;
    } catch (error) {
      console.error("Failed to exchange Privy token for JWT:", error);
      throw error;
    }
  }, []);

  const handlePrivyAuthSuccess = useCallback(async () => {
    if (!identityToken) {
      console.error("No identity token available");
      return;
    }

    try {
      const jwtToken = await exchangePrivyForJWT(identityToken);

      dispatch(setUserAuth({ accessToken: jwtToken }));

      if (currentAccount?.address) {
        disconnect();
      }

      Storage.setLoginMethod(LOGIN_METHODS.PRIVY);

      const redirectAfterLogin = Storage.getRedirectAfterLogin();

      if (redirectAfterLogin) {
        const location = `${window.location.pathname}${window.location.search}`;
        if (location !== redirectAfterLogin) {
          Storage.clearRedirectAfterLogin();
          window.location.href = redirectAfterLogin;
        }
      } else {
        window.location.href = "/new-pairs";
      }

      closeSocketInstance(NETWORKS.SUI);
      createSocketInstance(NETWORKS.SUI, jwtToken);

      console.log("Privy authentication successful");
    } catch (error) {
      console.error("Privy authentication failed:", error);
    }
  }, [
    identityToken,
    exchangePrivyForJWT,
    dispatch,
    currentAccount?.address,
    disconnect,
  ]);

  useEffect(() => {
    if (authenticated && identityToken && privyUser && !userId) {
      handlePrivyAuthSuccess();
    }
  }, [authenticated, identityToken, privyUser, userId, handlePrivyAuthSuccess]);

  const onPrivyLogin = useCallback(() => {
    if (!authenticated) {
      login();
      return;
    }
  }, [login, authenticated]);

  const handlerAddSignerSession = useCallback(
    async (address: string) => {
      try {
        if (!walletsReady) {
          console.warn(
            "Wallet proxy not ready yet, skipping session signer setup"
          );
          return;
        }

        await addSessionSigners({
          signers: [
            {
              signerId: config.privyConfig.signerId,
            },
          ],
          address,
        });
      } catch (error) {
        console.error("Failed to add Privy signer:", error);
      }
    },
    [walletsReady, addSessionSigners]
  );

  const handlerRemoveSignerSession = useCallback(
    async (address: string) => {
      try {
        await removeSessionSigners({
          address,
        });
      } catch (error) {
        console.error("Failed to remove Privy signer:", error);
      }
    },
    [removeSessionSigners]
  );

  const createPrivyWallet = useCallback(async (numberWallets: number) => {
    try {
      const response = await rf
        .getRequest("PrivyRequest")
        .createWallet(numberWallets);

      // Note: Session signers are no longer automatically added here
      // Users must manually enable trading via the wallet manager
      return response;
    } catch (error) {
      console.error("Failed to create Privy wallet:", error);
      throw error;
    }
  }, []);

  // Removed automatic session signer addition on login
  // Users must now manually enable trading via the wallet manager

  return {
    onPrivyLogin,
    authenticated,
    user: privyUser,
    identityToken,
    handlerAddSignerSession,
    createPrivyWallet,
    handlerRemoveSignerSession,
  };
};
